# AI 页面 API 厂商使用情况梳理

## 概述

本文档按页面维度梳理了 `apps/web/app/[locale]/(marketing)/ai` 目录下所有 AI 功能页面使用的 API 厂商和接口情况。

## 页面与厂商映射

### 🎭 Face Swap (换脸)

- **页面路径**: `/ai/face-swap`
- **任务类型**: `face-swap`
- **API 厂商**: **PiAPI**
- **调用接口**:
  - 生成: `/api/aiimage/generate`
  - 状态查询: `/api/aiimage/task`
  - 历史记录: `/api/history/getList`
- **输入参数**:
  - `swap_image` - 要替换的脸部图像
  - `target_image` - 目标图像
- **输出**: `image_url` - 换脸后的图像

---

### 🤗 AI Hug (AI 拥抱视频)

- **页面路径**: `/ai/ai-hug`
- **任务类型**: `ai_hug`
- **API 厂商**: **PiAPI**
- **调用接口**:
  - 生成: `/api/aiimage/generate`
  - 状态查询: `/api/aiimage/task`
  - 历史记录: `/api/history/getList`
- **输入参数**:
  - `image_url` - 源图像
- **输出**: `video_url` - 生成的拥抱视频

---

### 😊 AI Smile (AI 微笑视频)

- **页面路径**: `/ai/ai-smile`
- **任务类型**: `aismile`
- **API 厂商**: **KieAI**
- **调用接口**:
  - 生成: `/api/video/aismile`
  - 状态查询: `/api/video/detail`
  - 历史记录: `/api/history/getList`
- **输入参数**:
  - `imageUrl` - 源图像
  - `prompt` - 提示词
- **输出**: `videoUrl` - 生成的微笑视频
- **默认配置**: 时长 5 秒，720p 质量，9:16 宽高比

---

### 🎬 Image to Video (图像转视频)

- **页面路径**: `/ai/image-to-video`
- **任务类型**: `imagetovideo`
- **API 厂商**: **KieAI**
- **调用接口**:
  - 生成: `/api/video/imagetovideo`
  - 状态查询: `/api/video/detail`
  - 历史记录: `/api/history/getList`
- **输入参数**:
  - `imageUrl` - 源图像
  - `prompt` - 提示词（必需）
- **输出**: `videoUrl` - 生成的视频
- **默认配置**: 时长 5 秒，720p 质量，16:9 宽高比

---

### 🎨 Photo to Anime (照片转动漫)

- **页面路径**: `/ai/photo-to-anime`
- **任务类型**: `photo-to-anime`
- **API 厂商**: **KieAI**
- **调用接口**:
  - 生成: `/api/images/generate/photo-to-anime`
  - 状态查询: `/api/images/record-info`
  - 历史记录: `/api/history/getList`
- **输入参数**:
  - `filesUrl` - 源图像文件
  - `styleId` - 风格 ID（必需）
  - `customPrompt` - 自定义提示词
  - `aspectRatio` - 宽高比
- **输出**: `imageUrls` - 生成的动漫风格图像
- **默认配置**: 1:1 宽高比，生成 1 个变体

---

### 👕 Virtual Try-On (AI 试穿)

- **页面路径**: `/ai/virtual-try-on`
- **任务类型**: `ai_try_on`
- **API 厂商**: **Kling**
- **调用接口**:
  - 生成: `/api/kling/try-on`
  - 状态查询: `/api/kling/try-on`
  - 历史记录: `/api/history/getList`
- **输入参数**:
  - `model_input` - 人物图像
  - `dress_input` - 服装图像
- **输出**: `imageUrl` - 试穿效果图

---

### 📹 Memory Video (记忆视频)

- **页面路径**: `/ai/memory-video`
- **任务类型**: `memory_video`
- **API 厂商**: **PiAPI**
- **调用接口**:
  - 生成: `/api/aiimage/generate`
  - 状态查询: `/api/aiimage/task`
  - 历史记录: `/api/history/getList`
- **输入参数**:
  - `image_url` - 照片
- **输出**: `video_url` - 生成的记忆视频

---

### ✏️ Sketch to Image (草图转图像)

- **页面路径**: `/ai/sketch-to-image`
- **任务类型**: `image-expansion`
- **API 厂商**: **KieAI**
- **调用接口**:
  - 生成: `/api/images/generate/photo-to-anime`
  - 状态查询: `/api/images/record-info`
  - 历史记录: `/api/history/getList`
- **输入参数**:
  - `filesUrl` - 源图像文件（必需）
  - `aspectRatio` - 宽高比
  - `prompt` - 提示词
- **输出**: `imageUrls` - 扩展后的图像
- **默认配置**: 1:1 宽高比，生成 1 个变体

---

### 其他页面

以下页面可能使用不同的实现方式或外部服务：

#### 📝 Text to Image (文本转图像)

- **页面路径**: `/ai/text-to-image`
- **实现方式**: 使用独立的 ClientContainer 组件
- **可能的厂商**: 需要进一步检查 ClientContainer 实现

#### 🎭 Ghibli Style (吉卜力风格)

- **页面路径**: `/ai/ghibli`
- **实现方式**: 使用独立的 ClientContainer 组件
- **可能的厂商**: 需要进一步检查 ClientContainer 实现

#### 其他工具页面

- `/ai/photo-restoration` - 照片修复
- `/ai/photo-colorizer` - 照片上色
- `/ai/old-filter` - 老化滤镜
- `/ai/image-upscaler` - 图像放大
- `/ai/image-to-image` - 图像转图像
- `/ai/background-removal` - 背景移除
- `/ai/test-consume` - 测试消费

_注：这些页面的具体实现需要进一步分析_

## 厂商使用统计

### PiAPI (3 个页面)

- Face Swap (换脸)
- AI Hug (AI 拥抱视频)
- Memory Video (记忆视频)

### KieAI (4 个页面)

- AI Smile (AI 微笑视频)
- Image to Video (图像转视频)
- Photo to Anime (照片转动漫)
- Sketch to Image (草图转图像)

### Kling (1 个页面)

- Virtual Try-On (AI 试穿)

## 技术架构

### 统一组件系统

所有页面都使用统一的组件和 hooks：

- `useUnifiedGeneration` - 统一生成 hook
- `useUnifiedHistory` - 统一历史记录 hook
- `UnifiedHistoryTab` - 统一历史记录标签
- `GenerationProgress` - 生成进度组件

### 轮询配置

所有任务都使用统一的轮询配置：

- **轮询间隔**: 5 秒
- **超时时间**: 10 分钟
- **最大重试**: 120 次

### 适配器模式

通过适配器工厂 `AdapterFactory` 根据任务类型自动选择对应的 API 适配器：

- `PiApiAdapter` - 处理 PiAPI 相关任务
- `KieAiAdapter` - 处理 KieAI 相关任务
- `KlingAdapter` - 处理 Kling 相关任务

## 接口调用流程

1. **页面初始化** → 确定 `TASK_TYPE`
2. **适配器选择** → `AdapterFactory.getAdapter(taskType)`
3. **参数处理** → 根据 `fieldMapping` 转换输入参数
4. **API 调用** → 调用对应厂商的生成接口
5. **状态轮询** → 定期查询任务状态
6. **结果处理** → 根据 `fieldMapping` 处理输出结果
7. **历史保存** → 保存到统一的历史记录系统

## 总结

项目采用了多厂商策略，根据不同功能的特点选择最适合的 API 厂商：

- **PiAPI**: 专长于图像处理和简单视频生成
- **KieAI**: 专注于复杂视频生成和图像风格转换
- **Kling**: 专门提供 AI 试穿等特殊功能

这种架构设计实现了功能多样性和技术冗余，确保了服务的稳定性和功能的丰富性。
