'use client'

import { <PERSON> } from '@i18n/routing'
import { usePathname } from '@i18n/routing'
import { LocaleSwitch } from '@shared/components/LocaleSwitch'
import { Logo } from '@shared/components/Logo'
import { ToolsDropdownMenu } from '@shared/components/ToolsDropdownMenu'
import { VideoDropdownMenu } from '@shared/components/VideoDropdownMenu'
import { ResourcesDropdownMenu } from '@shared/components/ResourcesDropdownMenu'
import { Button } from '@ui/components/button'
import { Sheet, SheetContent, SheetTrigger } from '@ui/components/sheet'
import { cn } from '@ui/lib'
import { MenuIcon } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useDebounceCallback } from 'usehooks-ts'
import { getUserFromClientCookies } from '../../../../app/utils/client-cookies'
import Cookies from 'js-cookie'

import { User, LogOut, ChevronDown, Sun, Moon } from 'lucide-react'
import { analyticsService } from '@/services/analytics'
import { useAtom } from 'jotai'
import {
  UserInfo,
  userInfoAtom,
  themeAtom,
  toggleThemeAtom,
  userThemeOverrideAtom,
  PATH_THEME_PRIORITY_HIGHER,
} from '@marketing/stores'
import { Avatar } from './Avatar'
import { useRouter } from 'next/navigation'

function UserMenu({ userInfo }: { userInfo: UserInfo }) {
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const t = useTranslations()

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLogout = () => {
    Cookies.remove('oauth_avatar')
    Cookies.remove('oauth_email')
    Cookies.remove('oauth_id')
    window.location.href = '/'
  }

  return (
    <div className="flex items-center gap-2">
      {/* <Button asChild variant='secondary' className='hidden md:flex'>
        <Link href='/app'>{t('common.menu.dashboard')}</Link>
      </Button> */}

      <div className="relative" ref={menuRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-between font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-500/50 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-11 rounded-full text-sm whitespace-nowrap px-3 bg-gray-800/60 hover:bg-gray-700/70 text-gray-100 transition-all border border-purple-500/30 shadow-sm hover:shadow md:min-w-[180px]"
        >
          <div className="flex items-center gap-2.5">
            <Avatar src={userInfo.avatar_url} alt={userInfo.email} />
            <span className="hidden lg:block text-sm font-medium truncate pr-2 max-w-[100px] text-white">
              {userInfo.email.split('@')[0]}
            </span>
          </div>
          <ChevronDown
            className={cn(
              'h-4 w-4 transition-transform duration-200 text-purple-300',
              isOpen && 'rotate-180'
            )}
          />
        </button>

        {isOpen && (
          <div className="absolute right-0 mt-2 w-64 origin-top-right rounded-lg border border-purple-500/30 shadow-lg animate-in fade-in-0 zoom-in-95 bg-gray-800/90 backdrop-blur-md">
            <div className="px-4 py-3">
              <p className="text-sm font-medium text-white">{userInfo.email}</p>
              <p className="text-xs text-purple-300">
                {t('common.points')}: {userInfo?.points ?? 0}
              </p>
            </div>

            <div className="border-t border-purple-500/30">
              <div className="flex flex-col py-2">
                <Link
                  href="/auth/login"
                  className="flex items-center px-4 py-2 text-sm text-gray-200 hover:bg-purple-900/50 transition-colors duration-200"
                >
                  <User className="mr-2 h-4 w-4 text-purple-300" />
                  {t('common.menu.profile')}
                </Link>
              </div>
            </div>

            <div className="border-t border-purple-500/30 py-2">
              <button
                onClick={handleLogout}
                className="flex w-full items-center px-4 py-2 text-sm text-pink-300 hover:bg-purple-900/50 transition-colors duration-200"
              >
                <LogOut className="mr-2 h-4 w-4" />
                {t('common.menu.logout')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// 主题切换组件
function ThemeToggle() {
  const [theme] = useAtom(themeAtom)
  const [, toggleTheme] = useAtom(toggleThemeAtom)
  const [userOverride] = useAtom(userThemeOverrideAtom)
  const pathname = usePathname()

  // 判断是否为AI页面
  const isAiPage = pathname
    .replace(/^\/[a-z]{2}(-[A-Z]{2})?/, '')
    .startsWith('/ai/')

  // 判断当前是否使用了用户覆盖设置
  const isUsingUserOverride = !!userOverride

  const handleToggle = () => {
    const newTheme = toggleTheme()

    // 用户手动切换时的提示
    if (process.env.NODE_ENV === 'development') {
      console.log(
        `🎨 用户手动切换主题: ${theme} → ${newTheme} (页面: ${
          isAiPage ? 'AI' : '普通'
        })`
      )

      if (PATH_THEME_PRIORITY_HIGHER) {
        console.log(
          '⚠️ 注意：当前配置为路径优先级更高，用户设置可能会被路径覆盖'
        )
      }
    }
  }

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={handleToggle}
      className={
        theme === 'light'
          ? 'border-blue-200 bg-white/60 hover:bg-blue-50'
          : 'border-purple-500/30 bg-gray-800/60 hover:bg-gray-700/70'
      }
      title={`当前主题: ${theme === 'light' ? '亮色' : '暗色'}${
        PATH_THEME_PRIORITY_HIGHER
          ? ` (${isAiPage ? 'AI页面' : '普通页面'}强制)`
          : isUsingUserOverride
          ? ' (用户自定义)'
          : ` (${isAiPage ? 'AI页面' : '普通页面'}默认)`
      }`}
    >
      {theme === 'light' ? (
        <Moon className="h-4 w-4 text-blue-600" />
      ) : (
        <Sun className="h-4 w-4 text-purple-300" />
      )}
    </Button>
  )
}

export function NavBar() {
  const t = useTranslations()
  const router = useRouter()
  const [theme] = useAtom(themeAtom)
  const [userInfo, setUserInfo] = useAtom(userInfoAtom)
  const user = getUserFromClientCookies()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const pathname = usePathname()
  const [isTop, setIsTop] = useState(true)
  const lastPathnameRef = useRef(pathname)

  console.log('faith=============theme', theme)

  const handleRedirect = () => {
    const redirectPath = localStorage.getItem('REDIRECT_PATH')
    if (redirectPath) {
      console.log('handleRedirect redirectPath', redirectPath)
      localStorage.removeItem('REDIRECT_PATH')
      router.push(redirectPath)
    }
  }

  const getUserInfo = async () => {
    if (user && user.email) {
      const userInfoResponse = await fetch(
        `/api/user/info?email=${encodeURIComponent(user.email)}`
      )
      const userInfoData = await userInfoResponse.json()
      console.log('faith=============userInfoData', userInfoData)
      if (userInfoData.success) {
        setUserInfo(userInfoData.data)
        analyticsService.trackUserVisit(userInfoData.data.id)
        handleRedirect()
      }
    }
  }

  const debouncedScrollHandler = useDebounceCallback(
    () => {
      setIsTop(window.scrollY <= 10)
    },
    150,
    {
      maxWait: 150,
    }
  )

  const menuItems: {
    label: string
    href?: string
    isDropdown?: boolean
    isVideoDropdown?: boolean
    isResourcesDropdown?: boolean
    isHot?: boolean
  }[] = [
    // {
    //   label: t('homepage'),
    //   href: '/',
    // },
    {
      label: t('common.menu.templates'),
      href: '/templates',
      isHot: true,
    },
    {
      label: t('common.menu.aiTools'),
      isDropdown: true,
    },
    {
      label: t('common.menu.aiVideo'),
      isDropdown: true,
      isVideoDropdown: true,
    },
    {
      label: t('common.menu.resources'),
      isDropdown: true,
      isResourcesDropdown: true,
    },
    // {
    //   label: t('common.menu.imageGenerator'),
    //   href: '/ai-generate-image',
    // },
    // {
    //   label: t('common.menu.tattooGenerator'),
    //   href: '/ai-tattoo-generator',
    // },

    {
      label: t('common.menu.pricing'),
      href: '/pricing',
    },
    {
      label: t('contact.text'),
      href: '/contact',
    },
  ]

  const isMenuItemActive = (href: string) => {
    // 对于首页特殊处理
    if (href === '/') {
      return pathname === '/'
    }
    // 其他页面精确匹配
    return pathname === href
  }

  const saveRedirectPath = useCallback(() => {
    localStorage.setItem('REDIRECT_PATH', pathname)
  }, [pathname])

  useEffect(() => {
    window.addEventListener('scroll', debouncedScrollHandler)
    debouncedScrollHandler()
    return () => {
      window.removeEventListener('scroll', debouncedScrollHandler)
    }
  }, [debouncedScrollHandler])

  useEffect(() => {
    setMobileMenuOpen(false)
  }, [pathname])

  // 更新路径引用
  useEffect(() => {
    lastPathnameRef.current = pathname
  }, [pathname])

  useEffect(() => {
    getUserInfo()
  }, [])

  return (
    <nav
      className={`fixed top-0 left-0 z-50 w-full transition-all duration-300 ${
        isTop
          ? 'shadow-none'
          : theme === 'light'
          ? 'bg-white/80 shadow-lg shadow-blue-500/10 backdrop-blur-lg'
          : 'bg-gray-900/40 shadow-lg shadow-purple-500/20 backdrop-blur-lg'
      }`}
      data-test="navigation"
    >
      <div className="w-full max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8 overflow-visible">
        <div
          className={`flex items-center justify-between ${
            isTop ? 'py-4' : 'py-4'
          } transition-[padding] duration-200 overflow-visible`}
        >
          {/* 左侧布局：Logo + 菜单 */}
          <div className="flex items-center gap-8 overflow-visible flex-shrink-0">
            <Logo withLabel />

            {/* 菜单项暗黑风格 */}
            <div className="hidden items-center lg:flex overflow-visible">
              <div className="flex items-center gap-0 overflow-visible">
                {menuItems.map((menuItem) => {
                  if (menuItem.isDropdown) {
                    if (menuItem.isVideoDropdown) {
                      return (
                        <VideoDropdownMenu key={menuItem.label}>
                          <span
                            className={`flex items-center px-3 py-2 font-medium text-sm whitespace-nowrap flex-shrink-0 cursor-pointer transition-colors ${
                              theme === 'light'
                                ? 'text-gray-700 hover:text-blue-600'
                                : 'text-gray-200/90 hover:text-white'
                            }`}
                          >
                            {/* {theme} */}
                            {menuItem.label}
                            <ChevronDown className="ml-1 h-3 w-3" />
                          </span>
                        </VideoDropdownMenu>
                      )
                    } else if (menuItem.isResourcesDropdown) {
                      return (
                        <ResourcesDropdownMenu key={menuItem.label}>
                          <span
                            className={`flex items-center px-3 py-2 font-medium text-sm whitespace-nowrap flex-shrink-0 cursor-pointer transition-colors ${
                              theme === 'light'
                                ? 'text-gray-700 hover:text-blue-600'
                                : 'text-gray-200/90 hover:text-white'
                            }`}
                          >
                            {menuItem.label}
                            <ChevronDown className="ml-1 h-3 w-3" />
                          </span>
                        </ResourcesDropdownMenu>
                      )
                    } else {
                      return (
                        <ToolsDropdownMenu key={menuItem.label}>
                          <span
                            className={`flex items-center px-3 py-2 font-medium text-sm whitespace-nowrap flex-shrink-0 cursor-pointer transition-colors ${
                              theme === 'light'
                                ? 'text-gray-700 hover:text-blue-600'
                                : 'text-gray-200/90 hover:text-white'
                            }`}
                          >
                            {menuItem.label}
                            <ChevronDown className="ml-1 h-3 w-3" />
                          </span>
                        </ToolsDropdownMenu>
                      )
                    }
                  }

                  return (
                    <Link
                      key={menuItem.href + menuItem.label}
                      href={menuItem.href!}
                      className={cn(
                        'block px-3 py-2 font-medium text-sm whitespace-nowrap flex-shrink-0 transition-colors',
                        isMenuItemActive(menuItem.href!)
                          ? theme === 'light'
                            ? 'font-bold text-blue-600'
                            : 'font-bold text-white'
                          : theme === 'light'
                          ? 'text-gray-700 hover:text-blue-600'
                          : 'text-gray-200/90 hover:text-white'
                      )}
                    >
                      <span className="flex items-center">
                        {menuItem.label}
                        {menuItem.isHot && (
                          <span className="inline-flex items-center justify-center bg-gradient-to-r from-orange-500 to-red-500 text-white px-1.5 py-0.5 text-xs font-bold rounded ml-1.5 shadow-sm">
                            {t('common.menu.hot')}
                          </span>
                        )}
                      </span>
                    </Link>
                  )
                })}
              </div>
            </div>
          </div>

          {/* 右侧布局 */}
          <div className="flex items-center justify-end gap-3 flex-shrink-0">
            <LocaleSwitch />
            {/* <ThemeToggle /> */}

            <Sheet
              open={mobileMenuOpen}
              onOpenChange={(open) => setMobileMenuOpen(open)}
            >
              <SheetTrigger asChild>
                <Button
                  size="icon"
                  variant="outline"
                  aria-label="Menu"
                  className={`lg:hidden transition-colors ${
                    theme === 'light'
                      ? 'border-blue-200 bg-white/60 hover:bg-blue-50'
                      : 'border-purple-500/30 bg-gray-800/60 hover:bg-gray-700/70'
                  }`}
                >
                  <MenuIcon
                    className={`size-4 ${
                      theme === 'light' ? 'text-blue-600' : 'text-purple-300'
                    }`}
                  />
                </Button>
              </SheetTrigger>
              <SheetContent
                className="w-[250px] border-l-0 bg-gray-900/95 backdrop-blur-md border-r border-purple-500/30 text-white"
                side="right"
              >
                <div className="flex flex-col items-start justify-center gap-1">
                  {menuItems.map((menuItem) => {
                    if (menuItem.isDropdown) {
                      if (menuItem.isVideoDropdown) {
                        return (
                          <Link
                            key={menuItem.label}
                            href="/templates"
                            className="block px-4 py-2.5 font-medium text-base rounded-xl transition-colors text-gray-300 hover:bg-purple-900/30"
                          >
                            {menuItem.label}
                          </Link>
                        )
                      } else if (menuItem.isResourcesDropdown) {
                        return (
                          <Link
                            key={menuItem.label}
                            href="/"
                            className="block px-4 py-2.5 font-medium text-base rounded-xl transition-colors text-gray-300 hover:bg-purple-900/30"
                          >
                            {menuItem.label}
                          </Link>
                        )
                      } else {
                        return (
                          <Link
                            key={menuItem.label}
                            href="/templates"
                            className="block px-4 py-2.5 font-medium text-base rounded-xl transition-colors text-gray-300 hover:bg-purple-900/30"
                          >
                            {menuItem.label}
                          </Link>
                        )
                      }
                    }

                    return (
                      <Link
                        key={menuItem.href}
                        href={menuItem.href!}
                        className={cn(
                          'block px-4 py-2.5 font-medium text-base rounded-xl transition-colors',
                          isMenuItemActive(menuItem.href!)
                            ? 'bg-purple-900/50 text-purple-200 font-bold'
                            : 'text-gray-300 hover:bg-purple-900/30'
                        )}
                      >
                        {menuItem.label}
                      </Link>
                    )
                  })}

                  <Link
                    key={userInfo ? 'dashboard' : 'login'}
                    href={userInfo ? '/app' : '/auth/login'}
                    className="block px-4 py-2.5 text-base rounded-xl text-gray-300 hover:bg-purple-900/30 transition-colors"
                    prefetch={!userInfo}
                    onClick={saveRedirectPath}
                  >
                    {userInfo
                      ? t('common.menu.dashboard')
                      : t('common.menu.login')}
                  </Link>
                </div>
              </SheetContent>
            </Sheet>

            {userInfo ? (
              <UserMenu userInfo={userInfo} />
            ) : (
              <Button
                key="login"
                className={cn(
                  'hidden lg:flex text-white border-0 shadow-md transition-colors',
                  theme === 'light'
                    ? 'bg-blue-600 hover:bg-blue-500 shadow-blue-500/20'
                    : 'bg-purple-600 hover:bg-purple-500 shadow-purple-500/20'
                )}
                asChild
                onClick={saveRedirectPath}
              >
                <Link href="/auth/login">{t('common.menu.login')}</Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}
