'use client'

import { useTranslations } from 'next-intl'
import { <PERSON>, <PERSON>, Zap, <PERSON>, Smile, ArrowRight } from 'lucide-react'
import Image from 'next/image'

export default function VideoCreationShowcase() {
  const t = useTranslations('videoCreationShowcase')

  console.log('33333', t('cases'))

  const cases = [
    {
      title: t('cases.0.title'),
      description: t('cases.0.description'),
      tags: [t('cases.0.tags.0'), t('cases.0.tags.1')],
      icon: <Heart className="w-6 h-6 text-pink-400" />,
      gradient: 'from-pink-500 to-rose-500',
      beforeImage:
        '/samples/memorial-video-maker/memorial-video-maker-before.png',
      afterVideo: '/gif/memorial-video-maker-after.gif',
      isVideo: false, // GIF文件
    },
    {
      title: t('cases.1.title'),
      description: t('cases.1.description'),
      tags: [t('cases.1.tags.0'), t('cases.1.tags.1')],
      icon: <Zap className="w-6 h-6 text-purple-400" />,
      gradient: 'from-purple-500 to-indigo-500',
      beforeImage:
        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/C1.jpeg',
      afterVideo:
        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/C2.mp4',
      isVideo: true, // MP4文件
    },
    {
      title: t('cases.2.title'),
      description: t('cases.2.description'),
      tags: [t('cases.2.tags.0'), t('cases.2.tags.1')],
      icon: <Heart className="w-6 h-6 text-orange-400" />,
      gradient: 'from-orange-500 to-red-500',
      beforeImage:
        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/E2.png',
      afterVideo:
        'https://res.cloudinary.com/dnwsyyrpt/image/upload/v1751803083/282911280527853_msmoqf.gif',
      isVideo: false, // GIF文件
    },
    {
      title: t('cases.3.title'),
      description: t('cases.3.description'),
      tags: [t('cases.3.tags.0'), t('cases.3.tags.1')],
      icon: <Smile className="w-6 h-6 text-yellow-400" />,
      gradient: 'from-yellow-500 to-orange-500',
      beforeImage: '/samples/smile-before.jpg',
      afterVideo: '/samples/smile-video.mp4',
      isVideo: true, // 保持原有的占位符
    },
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 via-slate-900 to-black">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t('title')}
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            {t('subtitle')}
          </p>
        </div>

        {/* Cases - Single Column Layout */}
        <div className="space-y-12">
          {cases.map((caseItem, index) => (
            <div
              key={index}
              className="group relative bg-gray-800/30 backdrop-blur-lg rounded-3xl overflow-hidden border border-gray-700/50 hover:border-purple-500/50 transition-all duration-500"
            >
              {/* Background Gradient */}
              <div
                className={`absolute inset-0 bg-gradient-to-br ${caseItem.gradient} opacity-5 group-hover:opacity-10 transition-opacity duration-500`}
              />

              {/* Content */}
              <div className="relative p-8 lg:p-12">
                {/* Header Section */}
                <div className="flex flex-col lg:flex-row lg:items-start gap-8">
                  {/* Left: Text Content */}
                  <div className="lg:w-1/3 space-y-6">
                    {/* Icon & Title */}
                    <div className="flex items-center gap-4">
                      <div
                        className={`p-3 rounded-full bg-gradient-to-r ${caseItem.gradient} shadow-lg`}
                      >
                        {caseItem.icon}
                      </div>
                      <h3 className="text-2xl lg:text-3xl font-bold text-white">
                        {caseItem.title}
                      </h3>
                    </div>

                    {/* Description */}
                    <p className="text-gray-300 text-lg leading-relaxed">
                      {caseItem.description}
                    </p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-3">
                      {caseItem.tags.map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          className="px-4 py-2 bg-purple-900/40 text-purple-300 text-sm font-medium rounded-full border border-purple-500/30 backdrop-blur-sm"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Right: Before/After Media */}
                  <div className="lg:w-2/3">
                    <div className="relative">
                      {/* Before/After Container */}
                      <div className="flex items-center gap-6">
                        {/* Before Image */}
                        <div className="flex-1">
                          <div className="relative aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl">
                            <Image
                              src={caseItem.beforeImage}
                              alt={`${caseItem.title} - Before`}
                              fill
                              unoptimized
                              className="object-cover transition-transform duration-500 group-hover:scale-105"
                            />
                            <div className="absolute top-4 left-4 bg-black/70 text-white text-sm font-medium px-3 py-1 rounded-full backdrop-blur-sm">
                              Before
                            </div>
                          </div>
                        </div>

                        {/* Arrow */}
                        <div className="flex-shrink-0 flex items-center justify-center">
                          <div className="p-4 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 shadow-lg">
                            <ArrowRight className="w-6 h-6 text-white" />
                          </div>
                        </div>

                        {/* After Video/GIF */}
                        <div className="flex-1">
                          <div className="relative aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl">
                            {caseItem.isVideo ? (
                              <video
                                src={caseItem.afterVideo}
                                autoPlay
                                loop
                                muted
                                playsInline
                                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                              />
                            ) : (
                              <Image
                                src={caseItem.afterVideo}
                                alt={`${caseItem.title} - After`}
                                fill
                                unoptimized
                                className="object-cover transition-transform duration-500 group-hover:scale-105"
                              />
                            )}
                            <div className="absolute top-4 left-4 bg-black/70 text-white text-sm font-medium px-3 py-1 rounded-full backdrop-blur-sm">
                              After
                            </div>

                            {/* Play Icon Overlay */}
                            <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-all duration-300">
                              <div className="p-4 rounded-full bg-white/20 backdrop-blur-sm">
                                <Play
                                  className="w-8 h-8 text-white"
                                  fill="currentColor"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center gap-3 bg-gradient-to-r from-purple-900/50 to-pink-900/50 backdrop-blur-lg border border-purple-500/30 rounded-full px-8 py-4 text-purple-300">
            <Star className="w-6 h-6" />
            <span className="text-base font-medium">
              All AI video generator tools are completely free
            </span>
          </div>
        </div>
      </div>
    </section>
  )
}
