'use client'

import { useEffect, useState } from 'react'
import { getUserFromClientCookies } from '@/utils/client-cookies'

export default function TestGoogleAuth() {
  const [user, setUser] = useState<any>(null)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
    console.log(message)
  }

  useEffect(() => {
    addLog('页面加载完成')
    
    // 检查用户状态
    const userData = getUserFromClientCookies()
    if (userData) {
      setUser(userData)
      addLog(`用户已登录: ${userData.email}`)
    } else {
      addLog('用户未登录')
    }

    // 监听Google One Tap相关的控制台日志
    const originalConsoleLog = console.log
    const originalConsoleError = console.error

    console.log = (...args) => {
      const message = args.join(' ')
      if (message.includes('Google One Tap') || message.includes('google.accounts')) {
        addLog(`Console: ${message}`)
      }
      originalConsoleLog.apply(console, args)
    }

    console.error = (...args) => {
      const message = args.join(' ')
      if (message.includes('Google One Tap') || message.includes('google.accounts')) {
        addLog(`Error: ${message}`)
      }
      originalConsoleError.apply(console, args)
    }

    return () => {
      console.log = originalConsoleLog
      console.error = originalConsoleError
    }
  }, [])

  const handleManualLogin = () => {
    addLog('手动跳转到Google OAuth登录')
    window.location.href = '/api/oauth/google'
  }

  const handleLogout = () => {
    addLog('执行登出操作')
    // 清除cookies
    document.cookie.split(";").forEach(function(c) { 
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
    });
    window.location.reload()
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">Google Authentication Test</h1>
        
        {/* 用户状态 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">用户状态</h2>
          {user ? (
            <div className="space-y-2">
              <p><strong>邮箱:</strong> {user.email}</p>
              <p><strong>ID:</strong> {user.id}</p>
              <p><strong>头像:</strong> <img src={user.avatar} alt="Avatar" className="inline w-8 h-8 rounded-full ml-2" /></p>
              <button 
                onClick={handleLogout}
                className="mt-4 bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
              >
                登出
              </button>
            </div>
          ) : (
            <div>
              <p className="text-gray-600 mb-4">未登录</p>
              <button 
                onClick={handleManualLogin}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                手动Google登录
              </button>
            </div>
          )}
        </div>

        {/* Google One Tap 说明 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Google One Tap 测试</h2>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Google One Tap应该在页面加载后自动显示（如果用户未登录且满足条件）</p>
            <p>• 点击One Tap登录后，应该自动验证JWT token并设置用户状态</p>
            <p>• 登录成功后页面会自动刷新显示用户信息</p>
            <p>• 如果One Tap没有显示，可能是因为：浏览器限制、已登录、或环境配置问题</p>
          </div>
        </div>

        {/* 日志显示 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">实时日志</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <p>等待日志...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">{log}</div>
              ))
            )}
          </div>
          <button 
            onClick={() => setLogs([])}
            className="mt-4 bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
          >
            清除日志
          </button>
        </div>
      </div>
    </div>
  )
}
