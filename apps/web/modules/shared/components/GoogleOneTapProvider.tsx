'use client'

import type { PropsWithChildren } from 'react'
import { useEffect, useState } from 'react'
import { googleOneTap } from '@/utils/google-one-tap-login'
import {
  getGoogleOneTapConfig,
  isGoogleOneTapSupported,
  getGoogleOneTapErrorMessage,
} from '@/utils/google-one-tap-config'
import { useAtom } from 'jotai'
import { userInfoAtom } from '@marketing/stores'

// 移动端检测函数
const isMobileDevice = (): boolean => {
  if (typeof window === 'undefined') return false

  // 检查用户代理字符串
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android',
    'webos',
    'iphone',
    'ipad',
    'ipod',
    'blackberry',
    'windows phone',
    'mobile',
    'tablet',
  ]

  const isMobileUA = mobileKeywords.some((keyword) =>
    userAgent.includes(keyword)
  )

  // 检查屏幕尺寸（移动端通常小于768px）
  const isMobileScreen = window.innerWidth < 768

  // 检查触摸支持
  const hasTouchSupport =
    'ontouchstart' in window || navigator.maxTouchPoints > 0

  // 综合判断：用户代理包含移动设备关键词 或者 (屏幕小且支持触摸)
  return isMobileUA || (isMobileScreen && hasTouchSupport)
}

export function GoogleOneTapProvider({ children }: PropsWithChildren) {
  // 使用状态来处理hydration问题
  const [isClient, setIsClient] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [userInfo] = useAtom(userInfoAtom)

  // 处理hydration问题：确保客户端渲染后再进行设备检测
  useEffect(() => {
    setIsClient(true)
    setIsMobile(isMobileDevice())
  }, [])

  // 处理One Tap登录成功的回调
  const handleOneTapSuccess = async (response: any) => {
    console.log('Google One Tap response:', response)

    try {
      // 调用后端API验证JWT token
      const verifyResponse = await fetch('/api/oauth/google/one-tap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          credential: response.credential,
        }),
      })

      const result = await verifyResponse.json()

      if (result.success) {
        console.log('Google One Tap login successful:', result.user)

        // 刷新页面以更新用户状态
        window.location.reload()
      } else {
        console.error('Google One Tap verification failed:', result.error)
      }
    } catch (error) {
      console.error('Error during Google One Tap verification:', error)
    }
  }

  useEffect(() => {
    if (!isClient || typeof window === 'undefined') return
    if (isMobile) return
    // 检查环境是否支持Google One Tap
    if (!isGoogleOneTapSupported()) return
    // 获取配置
    const config = getGoogleOneTapConfig()
    if (!config) return
    console.log(userInfo,'userInfo')
    if (userInfo && userInfo.email) return
    try {
      googleOneTap(config, handleOneTapSuccess)
    } catch (error) {
      const errorMessage = getGoogleOneTapErrorMessage(error)
      console.error('GoogleOneTapProvider: 初始化失败:', errorMessage)
    }
  }, [isClient, isMobile, userInfo])

  return <>{children}</>
}
